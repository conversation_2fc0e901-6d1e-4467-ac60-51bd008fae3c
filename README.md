# Terraform API Gateway with VPC Link and Load Balancer

This Terraform configuration creates an AWS API Gateway with VPC Link integration to an Application Load Balancer for a streaming audio service.

## Architecture

The infrastructure includes:

- **VPC**: Custom VPC with public and private subnets across multiple AZs
- **Application Load Balancer**: Internal ALB in private subnets
- **VPC Link**: Connects API Gateway to the internal load balancer
- **API Gateway**: RESTful API with all streaming service endpoints
- **Security Groups**: Proper security controls for ALB and application instances
- **CloudWatch**: Logging and monitoring for API Gateway

## API Endpoints

The configuration creates the following endpoints:

- `GET /` - Root endpoint
- `GET /ping` - Health check endpoint
- `POST /start_session` - Start a new session
- `POST /clear_session` - Clear current session
- `GET /get_models` - Get available streaming models
- `GET /get_stream_mode` - Get stream mode configuration
- `POST /pause_audio` - Pause/resume audio stream
- `POST /clear_audio` - Stop audio stream
- `POST /send_message` - Send message to streaming queue
- `POST /show_subtitle` - Toggle subtitle display
- `POST /edgetts/set_voice_name` - Set EdgeTTS voice
- `GET /edgetts/get_voice_names` - Get available EdgeTTS voices
- `GET /edgetts/get_audio_stream` - Get EdgeTTS audio stream

## Prerequisites

1. AWS CLI configured with appropriate credentials
2. Terraform >= 1.0 installed
3. Appropriate AWS permissions for creating VPC, ALB, API Gateway, and related resources

## Usage

1. **Clone and configure**:
   ```bash
   # Copy the example variables file
   cp terraform.tfvars.example terraform.tfvars
   
   # Edit terraform.tfvars with your specific values
   nano terraform.tfvars
   ```

2. **Initialize Terraform**:
   ```bash
   terraform init
   ```

3. **Plan the deployment**:
   ```bash
   terraform plan
   ```

4. **Apply the configuration**:
   ```bash
   terraform apply
   ```

5. **Get outputs**:
   ```bash
   terraform output
   ```

## Configuration Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `aws_region` | AWS region | `us-west-2` |
| `project_name` | Project name | `api-gateway-streaming` |
| `environment` | Environment name | `dev` |
| `vpc_cidr` | VPC CIDR block | `10.0.0.0/16` |
| `public_subnet_cidrs` | Public subnet CIDRs | `["********/24", "********/24"]` |
| `private_subnet_cidrs` | Private subnet CIDRs | `["*********/24", "*********/24"]` |
| `api_gateway_stage_name` | API Gateway stage | `v1` |
| `target_port` | Application port | `8000` |
| `health_check_path` | Health check path | `/ping` |
| `allowed_cidr_blocks` | Allowed IP ranges | `["0.0.0.0/0"]` |

## Outputs

After deployment, you'll get:

- API Gateway invoke URL
- Load balancer DNS name
- VPC and subnet IDs
- Security group IDs
- All endpoint URLs for easy testing

## Security Considerations

1. **Network Security**: The load balancer is internal (private subnets only)
2. **API Gateway**: Can be restricted by IP using `allowed_cidr_blocks`
3. **Security Groups**: Properly configured for minimal required access
4. **VPC Link**: Secure connection between API Gateway and internal resources

## Application Deployment

After the infrastructure is created, you need to:

1. **Deploy your application** to instances in the private subnets
2. **Register instances** with the target group:
   ```bash
   aws elbv2 register-targets --target-group-arn <target-group-arn> --targets Id=<instance-id>
   ```
3. **Ensure your application** listens on the configured port (default: 8000)
4. **Implement health check** endpoint at `/ping`

## Testing

Test the API Gateway endpoints:

```bash
# Health check
curl https://<api-gateway-id>.execute-api.<region>.amazonaws.com/v1/ping

# Get models
curl https://<api-gateway-id>.execute-api.<region>.amazonaws.com/v1/get_models

# Start session
curl -X POST https://<api-gateway-id>.execute-api.<region>.amazonaws.com/v1/start_session
```

## Monitoring

- **CloudWatch Logs**: API Gateway logs are stored in `/aws/apigateway/<project-name>-<environment>`
- **X-Ray Tracing**: Enabled for request tracing
- **Metrics**: API Gateway metrics are automatically collected

## Cleanup

To destroy the infrastructure:

```bash
terraform destroy
```

## Cost Optimization

- NAT Gateways are the most expensive component
- Consider using NAT instances for development environments
- API Gateway charges per request and data transfer
- VPC Link has a fixed hourly charge

## Troubleshooting

1. **VPC Link Status**: Check VPC Link is in "AVAILABLE" state
2. **Target Health**: Ensure targets are healthy in the target group
3. **Security Groups**: Verify proper ingress/egress rules
4. **DNS Resolution**: Check VPC DNS settings are enabled
