#!/bin/bash

# Terraform API Gateway Deployment Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if terraform is installed
check_terraform() {
    if ! command -v terraform &> /dev/null; then
        print_error "Terraform is not installed. Please install Terraform first."
        exit 1
    fi
    print_success "Terraform is installed: $(terraform version | head -n1)"
}

# Check if AWS CLI is configured
check_aws() {
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install AWS CLI first."
        exit 1
    fi
    
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS CLI is not configured. Please run 'aws configure' first."
        exit 1
    fi
    
    print_success "AWS CLI is configured for account: $(aws sts get-caller-identity --query Account --output text)"
}

# Create terraform.tfvars if it doesn't exist
setup_tfvars() {
    if [ ! -f "terraform.tfvars" ]; then
        print_warning "terraform.tfvars not found. Creating from example..."
        cp terraform.tfvars.example terraform.tfvars
        print_warning "Please edit terraform.tfvars with your specific values before proceeding."
        read -p "Press Enter to continue after editing terraform.tfvars..."
    else
        print_success "terraform.tfvars found"
    fi
}

# Initialize Terraform
init_terraform() {
    print_status "Initializing Terraform..."
    terraform init
    print_success "Terraform initialized"
}

# Plan Terraform deployment
plan_terraform() {
    print_status "Planning Terraform deployment..."
    terraform plan -out=tfplan
    print_success "Terraform plan completed"
}

# Apply Terraform deployment
apply_terraform() {
    print_status "Applying Terraform deployment..."
    terraform apply tfplan
    print_success "Terraform deployment completed"
}

# Show outputs
show_outputs() {
    print_status "Deployment outputs:"
    terraform output
    
    print_status "API Gateway URL:"
    terraform output -raw api_gateway_invoke_url
    echo ""
    
    print_status "Test the health endpoint:"
    API_URL=$(terraform output -raw api_gateway_invoke_url)
    echo "curl ${API_URL}/ping"
}

# Main deployment function
deploy() {
    print_status "Starting Terraform API Gateway deployment..."
    
    check_terraform
    check_aws
    setup_tfvars
    init_terraform
    plan_terraform
    
    echo ""
    print_warning "Review the plan above. Do you want to proceed with the deployment?"
    read -p "Type 'yes' to continue: " confirm
    
    if [ "$confirm" = "yes" ]; then
        apply_terraform
        show_outputs
        print_success "Deployment completed successfully!"
    else
        print_warning "Deployment cancelled"
        exit 0
    fi
}

# Destroy function
destroy() {
    print_warning "This will destroy all resources created by this Terraform configuration."
    read -p "Type 'yes' to confirm destruction: " confirm
    
    if [ "$confirm" = "yes" ]; then
        print_status "Destroying infrastructure..."
        terraform destroy
        print_success "Infrastructure destroyed"
    else
        print_warning "Destruction cancelled"
    fi
}

# Help function
show_help() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  deploy    Deploy the infrastructure (default)"
    echo "  destroy   Destroy the infrastructure"
    echo "  plan      Show deployment plan"
    echo "  output    Show deployment outputs"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 deploy"
    echo "  $0 destroy"
    echo "  $0 plan"
}

# Main script logic
case "${1:-deploy}" in
    "deploy")
        deploy
        ;;
    "destroy")
        destroy
        ;;
    "plan")
        check_terraform
        check_aws
        setup_tfvars
        init_terraform
        plan_terraform
        ;;
    "output")
        show_outputs
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
