# AWS Configuration
aws_region = "us-west-2"

# Project Configuration
project_name = "streaming-api"
environment  = "dev"

# VPC Configuration
vpc_cidr               = "10.0.0.0/16"
public_subnet_cidrs    = ["********/24", "********/24"]
private_subnet_cidrs   = ["*********/24", "*********/24"]

# API Gateway Configuration
api_gateway_stage_name = "v1"

# Application Configuration
target_port        = 8000
health_check_path  = "/ping"

# Security Configuration
allowed_cidr_blocks = ["0.0.0.0/0"]  # Restrict this to your IP ranges for production
