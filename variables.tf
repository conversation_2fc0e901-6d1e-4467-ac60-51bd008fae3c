variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-west-2"
}

variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "api-gateway-streaming"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "dev"
}

variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "public_subnet_cidrs" {
  description = "CIDR blocks for public subnets"
  type        = list(string)
  default     = ["********/24", "********/24"]
}

variable "private_subnet_cidrs" {
  description = "CIDR blocks for private subnets"
  type        = list(string)
  default     = ["*********/24", "*********/24"]
}

variable "api_gateway_stage_name" {
  description = "API Gateway stage name"
  type        = string
  default     = "v1"
}

variable "target_port" {
  description = "Port on which the application is running"
  type        = number
  default     = 8000
}

variable "health_check_path" {
  description = "Health check path for load balancer"
  type        = string
  default     = "/ping"
}

variable "allowed_cidr_blocks" {
  description = "CIDR blocks allowed to access the API Gateway"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}
